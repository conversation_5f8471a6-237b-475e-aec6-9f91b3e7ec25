# ChatApp

* A web based chat application where people can connect with each other.
* Users can create Unique rooms and share it people to have private converstions.
* Fuctionality to check if users are typing and current online users.
* Tools used JavaScript, NodeJs, Web Sockets, HTML, CSS.

Deployed on Heroku: https://a-chatting-app.herokuapp.com

Link for the tutorial: https://himanshuagarwal190.medium.com/building-a-chat-application-from-scratch-with-room-functionality-df3d1e4ef662